'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Upload,
  Download,
  RefreshCw,
  AlertCircle,
  Image as ImageIcon,
  Trash2,
  FileImage,
  Maximize2,
  Minimize2
} from 'lucide-react';
import imageCompression from 'browser-image-compression';

interface CompressionError {
  message: string;
  type: 'file' | 'compression' | 'validation';
}

interface ImageData {
  file: File;
  url: string;
  size: number;
  format: string;
  dimensions: { width: number; height: number };
}

interface CompressionResult {
  original: ImageData;
  compressed: File;
  compressedUrl: string;
  compressionRatio: number;
  sizeSaved: number;
}

// Utility functions
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
    };
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

const validateImageFile = (file: File): { isValid: boolean; error?: CompressionError } => {
  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: {
        message: 'File size too large. Maximum size is 10MB.',
        type: 'validation'
      }
    };
  }

  // Check file type
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!validTypes.includes(file.type)) {
    return {
      isValid: false,
      error: {
        message: 'Invalid file type. Please upload JPG, PNG, or WebP images.',
        type: 'validation'
      }
    };
  }

  return { isValid: true };
};

// Upload Handler Component
interface UploadHandlerProps {
  onFileSelect: (file: File) => void;
  isDragOver: boolean;
  setIsDragOver: (isDragOver: boolean) => void;
  isProcessing: boolean;
  error: CompressionError | null;
  processingStep?: string;
}

const UploadHandler: React.FC<UploadHandlerProps> = ({
  onFileSelect,
  isDragOver,
  setIsDragOver,
  isProcessing,
  error,
  processingStep
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const file = files[0];

    if (file) {
      onFileSelect(file);
    }
  };

  return (
    <div
      className={`relative transition-all duration-300 ${
        isDragOver ? 'ring-2 ring-purple-500 ring-opacity-50' : ''
      }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <div
        className={`border-2 border-dashed rounded-xl !p-8 text-center transition-all duration-300 cursor-pointer ${
          isDragOver
            ? 'border-purple-500/50 bg-purple-500/5'
            : error
            ? 'border-red-500/30 bg-red-500/5'
            : 'border-white/20 bg-white/5 hover:bg-white/10'
        }`}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/jpg,image/png,image/webp"
          onChange={handleFileUpload}
          className="hidden"
          disabled={isProcessing}
        />

        {isProcessing ? (
          <div className="text-center">
            <RefreshCw size={48} className="mx-auto !mb-4 text-purple-400 animate-spin" />
            <p className="text-lg font-medium text-gray-300">Processing image...</p>
            <p className="text-sm text-gray-500 !mt-2">
              {processingStep || 'This may take a moment'}
            </p>
          </div>
        ) : (
          <>
            <div className="!mb-4">
              {error ? (
                <AlertCircle size={48} className="mx-auto text-red-400" />
              ) : (
                <Upload size={48} className="mx-auto text-purple-400" />
              )}
            </div>
            <h3 className="text-lg font-semibold text-gray-300 !mb-2">
              {error ? 'Upload Failed' : 'Upload Image'}
            </h3>
            <p className="text-gray-400 !mb-4">
              {error
                ? error.message
                : 'Drag & drop an image here, or click to browse'}
            </p>
            <div className="text-sm text-gray-500">
              <p>Supports: JPG, PNG, WebP</p>
              <p>Max size: 10MB</p>
            </div>
          </>
        )}

        {isDragOver && (
          <div className="absolute inset-0 bg-purple-500/10 border-2 border-dashed border-purple-500/50 rounded-xl flex items-center justify-center">
            <div className="text-purple-400 text-center">
              <Upload size={32} className="mx-auto !mb-2" />
              <p className="text-sm font-medium">Drop image here</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Compression Slider Component
interface CompressionSliderProps {
  quality: number;
  onQualityChange: (quality: number) => void;
  isProcessing: boolean;
}

const CompressionSlider: React.FC<CompressionSliderProps> = ({
  quality,
  onQualityChange,
  isProcessing
}) => {
  return (
    <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl !p-4">
      <div className="flex items-center justify-between !mb-4">
        <label className="text-sm font-medium text-gray-300">
          Compression Quality
        </label>
        <span className="text-sm font-mono bg-purple-500/20 text-purple-400 !px-2 !py-1 rounded">
          {quality}%
        </span>
      </div>
      
      <div className="relative">
        <input
          type="range"
          min="10"
          max="100"
          value={quality}
          onChange={(e) => onQualityChange(Number(e.target.value))}
          disabled={isProcessing}
          className="w-full h-2 bg-white/10 rounded-lg appearance-none cursor-pointer slider"
        />
        <div className="flex justify-between text-xs text-gray-500 !mt-2">
          <span>10% (Max compression)</span>
          <span>100% (No compression)</span>
        </div>
      </div>
      
      <div className="!mt-3 text-xs text-gray-400">
        <p>Lower values = smaller file size, higher values = better quality</p>
      </div>
    </div>
  );
};

// Preview Component
interface PreviewComponentProps {
  result: CompressionResult | null;
  isFullscreen: boolean;
  onToggleFullscreen: () => void;
}

const PreviewComponent: React.FC<PreviewComponentProps> = ({
  result,
  isFullscreen,
  onToggleFullscreen
}) => {
  if (!result) {
    return (
      <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl !p-8 text-center">
        <ImageIcon size={48} className="mx-auto !mb-4 text-gray-500 opacity-50" />
        <p className="text-gray-400">Compressed image preview will appear here</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Comparison Stats */}
      <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl !p-4">
        <h3 className="text-lg font-semibold text-gray-300 !mb-4">Compression Results</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-400 !mb-1">Original Size</p>
            <p className="text-lg font-semibold text-white">
              {formatFileSize(result.original.size)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-400 !mb-1">Compressed Size</p>
            <p className="text-lg font-semibold text-green-400">
              {formatFileSize(result.compressed.size)}
            </p>
          </div>
        </div>
        
        <div className="!mt-4 !pt-4 border-t border-white/10">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-400">Size Reduction:</span>
            <span className="text-sm font-semibold text-green-400">
              {result.compressionRatio.toFixed(1)}% ({formatFileSize(result.sizeSaved)} saved)
            </span>
          </div>
        </div>
      </div>

      {/* Image Preview */}
      <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl overflow-hidden">
        <div className="flex items-center justify-between !p-4 border-b border-white/10">
          <h3 className="text-lg font-semibold text-gray-300">Preview</h3>
          <button
            onClick={onToggleFullscreen}
            className="flex items-center gap-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg !px-3 !py-2 text-sm transition-colors"
          >
            {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
            {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
          </button>
        </div>
        
        <div className={`grid ${isFullscreen ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'} gap-4 !p-4`}>
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-400">Original</p>
            <div className="relative bg-white/5 rounded-lg overflow-hidden">
              <img
                src={result.original.url}
                alt="Original"
                className="w-full h-auto max-h-64 object-contain"
              />
              <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs !px-2 !py-1 rounded">
                {result.original.dimensions.width} × {result.original.dimensions.height}
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-400">Compressed</p>
            <div className="relative bg-white/5 rounded-lg overflow-hidden">
              <img
                src={result.compressedUrl}
                alt="Compressed"
                className="w-full h-auto max-h-64 object-contain"
              />
              <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs !px-2 !py-1 rounded">
                {result.original.dimensions.width} × {result.original.dimensions.height}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function ImageCompressor() {
  const [originalImage, setOriginalImage] = useState<ImageData | null>(null);
  const [compressionResult, setCompressionResult] = useState<CompressionResult | null>(null);
  const [quality, setQuality] = useState(60);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<CompressionError | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [processingStep, setProcessingStep] = useState<string>('');

  // Compression logic
  const compressImage = useCallback(async (file: File, qualityPercent: number) => {
    setIsProcessing(true);
    setError(null);
    setProcessingStep('Preparing compression...');

    try {
      const options = {
        maxSizeMB: 10,
        maxWidthOrHeight: 4096,
        useWebWorker: true,
        quality: qualityPercent / 100,
        preserveExif: false,
        onProgress: (progress: number) => {
          setProcessingStep(`Compressing... ${Math.round(progress)}%`);
        },
      };

      setProcessingStep('Starting compression...');
      const compressedFile = await imageCompression(file, options);

      setProcessingStep('Finalizing...');
      const compressedUrl = URL.createObjectURL(compressedFile);

      const sizeSaved = file.size - compressedFile.size;
      const compressionRatio = (sizeSaved / file.size) * 100;

      setCompressionResult({
        original: originalImage!,
        compressed: compressedFile,
        compressedUrl,
        compressionRatio,
        sizeSaved,
      });
    } catch (err) {
      console.error('Compression failed:', err);
      setError({
        message: 'Failed to compress image. Please try again.',
        type: 'compression'
      });
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
    }
  }, [originalImage]);

  // Handle file selection
  const handleFileSelect = useCallback(async (file: File) => {
    setError(null);
    setCompressionResult(null);

    const validation = validateImageFile(file);
    if (!validation.isValid) {
      setError(validation.error!);
      return;
    }

    setIsProcessing(true);

    try {
      const dimensions = await getImageDimensions(file);
      const url = URL.createObjectURL(file);

      const imageData: ImageData = {
        file,
        url,
        size: file.size,
        format: file.type,
        dimensions,
      };

      setOriginalImage(imageData);

      // Auto-compress with default quality
      setTimeout(() => {
        compressImage(file, quality);
      }, 100);
    } catch (err) {
      setError({
        message: 'Failed to process image. Please try again.',
        type: 'file'
      });
      setIsProcessing(false);
    }
  }, [quality, compressImage]);

  // Handle quality change with debouncing
  const handleQualityChange = useCallback((newQuality: number) => {
    setQuality(newQuality);

    if (originalImage && !isProcessing) {
      // Debounce compression
      setTimeout(() => {
        compressImage(originalImage.file, newQuality);
      }, 300);
    }
  }, [originalImage, isProcessing, compressImage]);

  // Download compressed image
  const downloadCompressed = useCallback(() => {
    if (!compressionResult) return;

    try {
      const url = URL.createObjectURL(compressionResult.compressed);
      const a = document.createElement('a');
      a.href = url;

      // Generate filename
      const originalName = originalImage?.file.name || 'image';
      const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
      const ext = compressionResult.compressed.type.split('/')[1];
      a.download = `${nameWithoutExt}-compressed.${ext}`;

      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      setTimeout(() => URL.revokeObjectURL(url), 100);
    } catch (err) {
      setError({
        message: 'Failed to download image. Please try again.',
        type: 'file'
      });
    }
  }, [compressionResult, originalImage]);

  // Reset all
  const resetAll = useCallback(() => {
    if (originalImage?.url) {
      try {
        URL.revokeObjectURL(originalImage.url);
      } catch (error) {
        console.warn('Failed to revoke original image URL:', error);
      }
    }
    if (compressionResult?.compressedUrl) {
      try {
        URL.revokeObjectURL(compressionResult.compressedUrl);
      } catch (error) {
        console.warn('Failed to revoke compressed image URL:', error);
      }
    }

    setOriginalImage(null);
    setCompressionResult(null);
    setError(null);
    setQuality(60);
    setIsFullscreen(false);
  }, [originalImage, compressionResult]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + D to download
      if ((e.ctrlKey || e.metaKey) && e.key === 'd' && compressionResult) {
        e.preventDefault();
        downloadCompressed();
      }

      // Ctrl/Cmd + R to reset
      if ((e.ctrlKey || e.metaKey) && e.key === 'r' && originalImage) {
        e.preventDefault();
        resetAll();
      }

      // Escape to exit fullscreen
      if (e.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [compressionResult, originalImage, isFullscreen, downloadCompressed, resetAll]);

  // Cleanup URLs on unmount
  useEffect(() => {
    return () => {
      if (originalImage?.url) {
        try {
          URL.revokeObjectURL(originalImage.url);
        } catch (error) {
          console.warn('Failed to revoke original image URL:', error);
        }
      }
      if (compressionResult?.compressedUrl) {
        try {
          URL.revokeObjectURL(compressionResult.compressedUrl);
        } catch (error) {
          console.warn('Failed to revoke compressed image URL:', error);
        }
      }
    };
  }, [originalImage, compressionResult]);

  return (
    <div className="min-h-screen !pt-24 !pb-16 !px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center !mb-12"
        >
          <div className="flex items-center justify-center !mb-6">
            <div className="bg-gradient-to-r from-purple-500 to-indigo-500 !p-4 rounded-2xl">
              <ImageIcon size={32} className="text-white" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold !mb-4 bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
            Image Compressor
          </h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Compress images without losing quality. Supports JPEG, PNG, and WebP formats with real-time preview and adjustable compression levels.
          </p>
        </motion.div>

        {/* Controls */}
        {originalImage && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl !p-4 md:!p-6 !mb-8"
          >
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <FileImage size={20} className="text-purple-400" />
                  <span className="text-sm text-gray-300">
                    {originalImage.file.name}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  {originalImage.dimensions.width} × {originalImage.dimensions.height} • {formatFileSize(originalImage.size)}
                </div>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={downloadCompressed}
                  disabled={!compressionResult || isProcessing}
                  className="flex items-center gap-2 bg-green-500/20 hover:bg-green-500/30 disabled:bg-gray-500/20 disabled:cursor-not-allowed border border-green-500/30 disabled:border-gray-500/30 rounded-lg !px-4 !py-2 text-sm transition-colors"
                >
                  <Download size={16} />
                  Download
                </button>
                <button
                  onClick={resetAll}
                  className="flex items-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 rounded-lg !px-4 !py-2 text-sm transition-colors"
                >
                  <Trash2 size={16} />
                  Reset
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Main Content */}
        <div className="space-y-8">
          {!originalImage ? (
            /* Upload Section */
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <UploadHandler
                onFileSelect={handleFileSelect}
                isDragOver={isDragOver}
                setIsDragOver={setIsDragOver}
                isProcessing={isProcessing}
                error={error}
                processingStep={processingStep}
              />
            </motion.div>
          ) : (
            /* Compression Interface */
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Compression Controls */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="space-y-6"
              >
                <CompressionSlider
                  quality={quality}
                  onQualityChange={handleQualityChange}
                  isProcessing={isProcessing}
                />

                {/* Quick Presets */}
                <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl !p-4">
                  <h3 className="text-sm font-medium text-gray-300 !mb-3">Quick Presets</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      { label: 'High Quality', value: 90 },
                      { label: 'Balanced', value: 70 },
                      { label: 'Small Size', value: 40 },
                      { label: 'Tiny', value: 20 },
                    ].map((preset) => (
                      <button
                        key={preset.label}
                        onClick={() => handleQualityChange(preset.value)}
                        disabled={isProcessing}
                        className={`!px-3 !py-2 text-xs rounded-lg border transition-colors ${
                          quality === preset.value
                            ? 'bg-purple-500/20 border-purple-500/30 text-purple-400'
                            : 'bg-white/10 border-white/20 text-gray-400 hover:bg-white/20'
                        }`}
                      >
                        {preset.label}
                      </button>
                    ))}
                  </div>
                </div>
              </motion.div>

              {/* Preview */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="lg:col-span-2"
              >
                <PreviewComponent
                  result={compressionResult}
                  isFullscreen={isFullscreen}
                  onToggleFullscreen={() => setIsFullscreen(!isFullscreen)}
                />
              </motion.div>
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="!mt-8 bg-red-500/10 border border-red-500/30 rounded-xl !p-4"
          >
            <div className="flex items-start gap-3">
              <AlertCircle size={20} className="text-red-400 !mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-red-400 !mb-1">Compression Error</h3>
                <p className="text-red-300">{error.message}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Keyboard Shortcuts Help */}
        {originalImage && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="!mt-12 bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl !p-6"
          >
            <h3 className="text-lg font-semibold text-gray-300 !mb-4">Keyboard Shortcuts</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-3">
                <kbd className="bg-white/10 border border-white/20 rounded !px-2 !py-1 text-xs font-mono">
                  Ctrl/Cmd + D
                </kbd>
                <span className="text-gray-400">Download compressed image</span>
              </div>
              <div className="flex items-center gap-3">
                <kbd className="bg-white/10 border border-white/20 rounded !px-2 !py-1 text-xs font-mono">
                  Ctrl/Cmd + R
                </kbd>
                <span className="text-gray-400">Reset and upload new image</span>
              </div>
              <div className="flex items-center gap-3">
                <kbd className="bg-white/10 border border-white/20 rounded !px-2 !py-1 text-xs font-mono">
                  Escape
                </kbd>
                <span className="text-gray-400">Exit fullscreen preview</span>
              </div>
              <div className="flex items-center gap-3">
                <kbd className="bg-white/10 border border-white/20 rounded !px-2 !py-1 text-xs font-mono">
                  Drag & Drop
                </kbd>
                <span className="text-gray-400">Upload image files</span>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
