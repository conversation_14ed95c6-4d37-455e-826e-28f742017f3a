'use client';

import React from 'react';

interface JsonSyntaxHighlighterProps {
  json: string;
  className?: string;
}

// JSON syntax highlighting component
const JsonSyntaxHighlighter: React.FC<JsonSyntaxHighlighterProps> = ({ json, className = '' }) => {
  const highlightJson = (jsonString: string): JSX.Element[] => {
    if (!jsonString) return [];

    const tokens: JSX.Element[] = [];
    let currentIndex = 0;

    // Regular expressions for different JSON elements
    const patterns = {
      string: /"([^"\\]|\\.)*"/g,
      number: /-?\d+\.?\d*([eE][+-]?\d+)?/g,
      boolean: /\b(true|false)\b/g,
      null: /\bnull\b/g,
      key: /"([^"\\]|\\.)*"(?=\s*:)/g,
      punctuation: /[{}[\],]/g,
      colon: /:/g,
      whitespace: /\s+/g,
    };

    // Create a combined pattern to match all tokens
    const allPatterns = Object.entries(patterns).map(([type, pattern]) => ({
      type,
      pattern: new RegExp(pattern.source, 'g')
    }));

    // Find all matches and sort by position
    const matches: Array<{ type: string; match: RegExpMatchArray; start: number; end: number }> = [];
    
    allPatterns.forEach(({ type, pattern }) => {
      let match;
      while ((match = pattern.exec(jsonString)) !== null) {
        matches.push({
          type,
          match,
          start: match.index!,
          end: match.index! + match[0].length
        });
      }
    });

    // Sort matches by start position
    matches.sort((a, b) => a.start - b.start);

    // Remove overlapping matches (keep the first one)
    const filteredMatches = matches.filter((match, index) => {
      if (index === 0) return true;
      const prevMatch = matches[index - 1];
      return match.start >= prevMatch.end;
    });

    // Build highlighted tokens
    filteredMatches.forEach((match, index) => {
      // Add any unmatched text before this match
      if (match.start > currentIndex) {
        const text = jsonString.slice(currentIndex, match.start);
        tokens.push(
          <span key={`text-${currentIndex}`} className="text-gray-300">
            {text}
          </span>
        );
      }

      // Add the highlighted match
      const text = match.match[0];
      const colorClass = getColorClass(match.type);
      
      tokens.push(
        <span key={`${match.type}-${match.start}`} className={colorClass}>
          {text}
        </span>
      );

      currentIndex = match.end;
    });

    // Add any remaining text
    if (currentIndex < jsonString.length) {
      const text = jsonString.slice(currentIndex);
      tokens.push(
        <span key={`text-${currentIndex}`} className="text-gray-300">
          {text}
        </span>
      );
    }

    return tokens;
  };

  const getColorClass = (type: string): string => {
    switch (type) {
      case 'key':
        return 'text-blue-400 font-medium';
      case 'string':
        return 'text-green-400';
      case 'number':
        return 'text-orange-400';
      case 'boolean':
        return 'text-purple-400 font-medium';
      case 'null':
        return 'text-red-400 font-medium';
      case 'punctuation':
        return 'text-gray-400';
      case 'colon':
        return 'text-gray-400';
      default:
        return 'text-gray-300';
    }
  };

  return (
    <pre className={`font-mono text-sm leading-relaxed ${className}`}>
      <code>
        {highlightJson(json)}
      </code>
    </pre>
  );
};

export default JsonSyntaxHighlighter;
