'use client';

import { useState, useCallback, useRef, useMemo, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Copy,
  Download,
  Upload,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  FileText,
  Braces,
  Trash2,
  Eye,
  Code
} from 'lucide-react';
import JsonSyntaxHighlighter from './JsonSyntaxHighlighter';

interface JsonError {
  message: string;
  line?: number;
  column?: number;
  type: 'syntax' | 'validation' | 'file';
}

interface JsonValidationResult {
  isValid: boolean;
  formatted?: string;
  error?: JsonError;
}

// Enhanced JSON validation and formatting
const validateAndFormatJson = (jsonString: string, indentSize: number, minify: boolean = false): JsonValidationResult => {
  if (!jsonString.trim()) {
    return { isValid: true, formatted: '' };
  }

  try {
    // Parse JSON to validate syntax
    const parsed = JSON.parse(jsonString);

    // Format with specified indentation or minify
    const formatted = minify
      ? JSON.stringify(parsed)
      : JSON.stringify(parsed, null, indentSize);

    return { isValid: true, formatted };
  } catch (err) {
    let errorMessage = 'Invalid JSON format';
    let line: number | undefined;
    let column: number | undefined;

    if (err instanceof SyntaxError) {
      errorMessage = err.message;

      // Try to extract line and column from error message
      const lineMatch = errorMessage.match(/line (\d+)/i);
      const columnMatch = errorMessage.match(/column (\d+)/i);

      if (lineMatch) line = parseInt(lineMatch[1], 10);
      if (columnMatch) column = parseInt(columnMatch[1], 10);
    }

    return {
      isValid: false,
      error: {
        message: errorMessage,
        line,
        column,
        type: 'syntax'
      }
    };
  }
};

const JsonBeautifier = () => {
  const [inputJson, setInputJson] = useState('');
  const [outputJson, setOutputJson] = useState('');
  const [error, setError] = useState<JsonError | null>(null);
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [copied, setCopied] = useState(false);
  const [indentSize, setIndentSize] = useState(2);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showSyntaxHighlighting, setShowSyntaxHighlighting] = useState(true);
  const [isMinified, setIsMinified] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Performance optimization: Check if JSON is too large for syntax highlighting
  const isLargeJson = useMemo(() => {
    return outputJson.length > 50000; // 50KB threshold
  }, [outputJson]);

  // Memoize whether to show syntax highlighting based on size and user preference
  const shouldShowSyntaxHighlighting = useMemo(() => {
    return showSyntaxHighlighting && !isLargeJson;
  }, [showSyntaxHighlighting, isLargeJson]);

  // Calculate JSON statistics
  const jsonStats = useMemo(() => {
    if (!outputJson) return null;

    try {
      const parsed = JSON.parse(outputJson);
      const countObjects = (obj: any): { objects: number; arrays: number; properties: number } => {
        let objects = 0;
        let arrays = 0;
        let properties = 0;

        if (Array.isArray(obj)) {
          arrays++;
          obj.forEach(item => {
            const counts = countObjects(item);
            objects += counts.objects;
            arrays += counts.arrays;
            properties += counts.properties;
          });
        } else if (obj && typeof obj === 'object') {
          objects++;
          const keys = Object.keys(obj);
          properties += keys.length;
          keys.forEach(key => {
            const counts = countObjects(obj[key]);
            objects += counts.objects;
            arrays += counts.arrays;
            properties += counts.properties;
          });
        }

        return { objects, arrays, properties };
      };

      const counts = countObjects(parsed);
      const lines = outputJson.split('\n').length;
      const size = new Blob([outputJson]).size;

      return {
        ...counts,
        lines,
        size: size < 1024 ? `${size} B` : size < 1024 * 1024 ? `${(size / 1024).toFixed(1)} KB` : `${(size / (1024 * 1024)).toFixed(1)} MB`
      };
    } catch {
      return null;
    }
  }, [outputJson]);

  // Format JSON with enhanced validation and performance optimization
  const formatJson = useCallback((jsonString: string) => {
    setIsProcessing(true);

    // Use setTimeout to prevent blocking the UI for large JSON
    setTimeout(() => {
      try {
        const result = validateAndFormatJson(jsonString, indentSize, isMinified);

        if (result.isValid) {
          setOutputJson(result.formatted || '');
          setError(null);
          setIsValid(jsonString.trim() ? true : null);
        } else {
          setOutputJson('');
          setError(result.error || null);
          setIsValid(false);
        }
      } catch (err) {
        setError({
          message: 'Processing failed. JSON might be too large or complex.',
          type: 'validation'
        });
        setOutputJson('');
        setIsValid(false);
      } finally {
        setIsProcessing(false);
      }
    }, 0);
  }, [indentSize, isMinified]);

  // Debounced input handler for better performance
  const [debouncedInput, setDebouncedInput] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      if (debouncedInput !== inputJson) {
        formatJson(inputJson);
        setDebouncedInput(inputJson);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [inputJson, formatJson, debouncedInput]);

  // Handle input change (now uses debounced formatting)
  const handleInputChange = (value: string) => {
    setInputJson(value);
    // formatJson will be called via useEffect with debouncing
  };

  // Copy to clipboard with enhanced error handling
  const copyToClipboard = async () => {
    if (!outputJson) return;

    try {
      // Try modern clipboard API first
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(outputJson);
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = outputJson;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
      }

      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
      setError({
        message: 'Failed to copy to clipboard. Please try selecting and copying manually.',
        type: 'validation'
      });
    }
  };

  // Download as JSON file with enhanced error handling
  const downloadJson = () => {
    if (!outputJson) return;

    try {
      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `formatted-json-${timestamp}.json`;

      // Create blob with proper MIME type
      const blob = new Blob([outputJson], {
        type: 'application/json;charset=utf-8'
      });

      // Check if blob was created successfully
      if (blob.size === 0) {
        throw new Error('Failed to create file');
      }

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      a.style.display = 'none';

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      // Clean up the URL object
      setTimeout(() => URL.revokeObjectURL(url), 100);

    } catch (err) {
      console.error('Failed to download:', err);
      setError({
        message: 'Failed to download file. Please try copying the content manually.',
        type: 'validation'
      });
    }
  };

  // Handle file upload with enhanced error handling
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      setError({
        message: 'File size too large. Maximum size is 10MB.',
        type: 'file'
      });
      return;
    }

    // Validate file type
    const validTypes = ['application/json', 'text/plain', 'text/json'];
    if (!validTypes.includes(file.type) && !file.name.endsWith('.json')) {
      setError({
        message: 'Invalid file type. Please upload a JSON or text file.',
        type: 'file'
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        handleInputChange(content);
      } catch (err) {
        setError({
          message: 'Failed to read file content.',
          type: 'file'
        });
      }
    };

    reader.onerror = () => {
      setError({
        message: 'Failed to read file.',
        type: 'file'
      });
    };

    reader.readAsText(file);
  };

  // Clear all content
  const clearAll = () => {
    setInputJson('');
    setOutputJson('');
    setError(null);
    setIsValid(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Load sample JSON
  const loadSampleJson = () => {
    const sampleJson = `{
  "name": "John Doe",
  "age": 30,
  "email": "<EMAIL>",
  "address": {
    "street": "123 Main St",
    "city": "New York",
    "zipCode": "10001",
    "country": "USA"
  },
  "hobbies": ["reading", "swimming", "coding"],
  "isActive": true,
  "lastLogin": "2024-01-15T10:30:00Z",
  "preferences": {
    "theme": "dark",
    "notifications": {
      "email": true,
      "push": false,
      "sms": true
    }
  }
}`;
    handleInputChange(sampleJson);
  };

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const file = files[0];

    if (file) {
      // Create a mock event for handleFileUpload
      const mockEvent = {
        target: { files: [file] }
      } as unknown as React.ChangeEvent<HTMLInputElement>;

      handleFileUpload(mockEvent);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + Enter to format
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        formatJson(inputJson);
      }

      // Ctrl/Cmd + K to clear
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        clearAll();
      }

      // Ctrl/Cmd + D to download
      if ((e.ctrlKey || e.metaKey) && e.key === 'd' && outputJson) {
        e.preventDefault();
        downloadJson();
      }

      // Ctrl/Cmd + C to copy (when output is focused)
      if ((e.ctrlKey || e.metaKey) && e.key === 'c' && outputJson && document.activeElement?.getAttribute('aria-label')?.includes('output')) {
        e.preventDefault();
        copyToClipboard();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [inputJson, outputJson, formatJson, clearAll, downloadJson, copyToClipboard]);

  useEffect(() => {
    formatJson(inputJson);
  }, [isMinified, indentSize, showSyntaxHighlighting, formatJson, inputJson]);

  return (
    <div className="min-h-screen !pt-24 !pb-16 !px-4">
      <div className="max-w-7xl mx-auto!">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center !mb-12"
        >
          <div className="flex items-center justify-center !mb-6">
            <div className="bg-gradient-to-r from-emerald-500 to-teal-500 !p-4 rounded-2xl">
              <Braces size={32} className="text-white" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold !mb-4 bg-gradient-to-r from-emerald-400 to-teal-400 bg-clip-text text-transparent">
            JSON Beautifier
          </h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto!">
            Format, validate, and beautify your JSON data with syntax highlighting and error detection.
            Supports large files, drag & drop, and keyboard shortcuts.
          </p>
        </motion.div>

        {/* Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl !p-4 md:!p-6 !mb-8"
        >
          <div className="flex flex-row flex-wrap items-start justify-center sm:items-center gap-4">
            {/* Format Options */}
            <div className="flex items-center gap-4">
              {/* Minify Toggle */}
              <button
                onClick={() => setIsMinified(!isMinified)}
                className={`flex items-center gap-2 border rounded-lg !px-3 !py-2 text-sm transition-colors ${isMinified
                    ? 'bg-orange-500/20 border-orange-500/30 text-orange-400'
                    : 'bg-white/10 border-white/20 text-gray-400 hover:bg-white/20'
                  }`}
              >
                <span className="text-xs font-mono">{'{}'}</span>
                Minify
              </button>

              {/* Indent Size - only show when not minified */}
              {!isMinified && (
                <div className="flex items-center gap-2">
                  <label className="text-sm text-gray-400 whitespace-nowrap">Indent:</label>
                  <select
                    value={indentSize}
                    onChange={(e) => setIndentSize(Number(e.target.value))}
                    className="bg-white/10 border border-white/20 rounded-lg !px-3 !py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500"
                  >
                    <option value={2}>2 spaces</option>
                    <option value={4}>4 spaces</option>
                    <option value={8}>8 spaces</option>
                  </select>
                </div>
              )}
            </div>

            {/* File Upload */}
            <button
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center gap-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg !px-4 !py-2 text-sm transition-colors whitespace-nowrap"
            >
              <Upload size={16} />
              <span className="hidden sm:inline">Upload JSON</span>
              <span className="sm:hidden">Upload</span>
            </button>
            <input
              ref={fileInputRef}
              type="file"
              accept=".json,.txt,application/json,text/plain"
              onChange={handleFileUpload}
              className="hidden"
            />

            {/* Syntax Highlighting Toggle */}
            <button
              onClick={() => setShowSyntaxHighlighting(!showSyntaxHighlighting)}
              className={`flex items-center gap-2 border rounded-lg !px-4 !py-2 text-sm transition-colors ${showSyntaxHighlighting
                  ? 'bg-emerald-500/20 border-emerald-500/30 text-emerald-400'
                  : 'bg-white/10 border-white/20 text-gray-400 hover:bg-white/20'
                }`}
            >
              <Eye size={16} />
              <span className="hidden sm:inline">Syntax Highlighting</span>
              <span className="sm:hidden">Syntax</span>
            </button>

            {/* Sample JSON */}
            <button
              onClick={loadSampleJson}
              className="flex items-center gap-2 bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/30 rounded-lg !px-4 !py-2 text-sm transition-colors"
            >
              <FileText size={16} />
              <span className="hidden sm:inline">Sample JSON</span>
              <span className="sm:hidden">Sample</span>
            </button>

            {/* Clear */}
            <button
              onClick={clearAll}
              className="flex items-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 rounded-lg !px-4 !py-2 text-sm transition-colors"
            >
              <Trash2 size={16} />
              Clear
            </button>

            {/* Status */}
            {isValid !== null && (
              <div className="flex items-center gap-2 sm:!ml-auto">
                {isValid ? (
                  <>
                    <CheckCircle size={16} className="text-green-400" />
                    <span className="text-sm text-green-400">Valid JSON</span>
                  </>
                ) : (
                  <>
                    <AlertCircle size={16} className="text-red-400" />
                    <span className="text-sm text-red-400">Invalid JSON</span>
                  </>
                )}
              </div>
            )}
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8">
          {/* Input Section */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-4!"
          >
            <h2 className="text-xl font-semibold text-gray-300">Input JSON</h2>
            <div
              className={`relative transition-all duration-300 ${isDragOver ? 'ring-2 ring-emerald-500 ring-opacity-50' : ''
                }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <textarea
                value={inputJson}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder="Paste your JSON here or drag & drop a JSON file..."
                aria-label="JSON input area"
                aria-describedby="input-help"
                className={`w-full h-96 bg-white/5 backdrop-blur-xl border rounded-xl !p-4 text-white font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-300 ${isDragOver
                    ? 'border-emerald-500/50 bg-emerald-500/5'
                    : 'border-white/10'
                  }`}
              />
              <div id="input-help" className="sr-only">
                Enter or paste JSON data to format and validate. You can also drag and drop JSON files.
              </div>
              {isDragOver && (
                <div className="absolute inset-0 bg-emerald-500/10 border-2 border-dashed border-emerald-500/50 rounded-xl flex items-center justify-center">
                  <div className="text-emerald-400 text-center">
                    <Upload size={32} className="mx-auto! !mb-2" />
                    <p className="text-sm font-medium">Drop JSON file here</p>
                  </div>
                </div>
              )}
            </div>
          </motion.div>

          {/* Output Section */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="space-y-4!"
          >
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <h2 className="text-xl font-semibold text-gray-300">Formatted JSON</h2>
                {jsonStats && (
                  <div className="flex flex-wrap gap-4 !mt-2 text-xs text-gray-500">
                    <span>{jsonStats.lines} lines</span>
                    <span>{jsonStats.size}</span>
                    <span>{jsonStats.objects} objects</span>
                    <span>{jsonStats.arrays} arrays</span>
                    <span>{jsonStats.properties} properties</span>
                  </div>
                )}
              </div>
              <div className="flex gap-2">
                <button
                  onClick={copyToClipboard}
                  disabled={!outputJson}
                  className="flex items-center gap-2 bg-emerald-500/20 hover:bg-emerald-500/30 disabled:bg-gray-500/20 disabled:cursor-not-allowed border border-emerald-500/30 disabled:border-gray-500/30 rounded-lg !px-3 !py-2 text-sm transition-colors"
                >
                  {copied ? <CheckCircle size={16} /> : <Copy size={16} />}
                  {copied ? 'Copied!' : 'Copy'}
                </button>
                <button
                  onClick={downloadJson}
                  disabled={!outputJson}
                  className="flex items-center gap-2 bg-blue-500/20 hover:bg-blue-500/30 disabled:bg-gray-500/20 disabled:cursor-not-allowed border border-blue-500/30 disabled:border-gray-500/30 rounded-lg !px-3 !py-2 text-sm transition-colors"
                >
                  <Download size={16} />
                  Download
                </button>
              </div>
            </div>
            <div className="relative">
              <div
                className="w-full h-96 bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl overflow-hidden"
                role="region"
                aria-label="Formatted JSON output"
                aria-live="polite"
              >
                {isProcessing ? (
                  <div className="h-full flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <RefreshCw size={32} className="mx-auto! !mb-2 opacity-50 animate-spin" />
                      <p className="text-sm">Processing JSON...</p>
                    </div>
                  </div>
                ) : outputJson ? (
                  <>
                    {isLargeJson && showSyntaxHighlighting && (
                      <div className="bg-yellow-500/10 border-b border-yellow-500/20 !p-2 text-yellow-400 text-xs">
                        <AlertCircle size={14} className="inline !mr-1" />
                        Large JSON detected. Syntax highlighting disabled for better performance.
                      </div>
                    )}
                    {shouldShowSyntaxHighlighting ? (
                      <div className="h-full overflow-auto !p-4">
                        <JsonSyntaxHighlighter json={outputJson} />
                      </div>
                    ) : (
                      <textarea
                        value={outputJson}
                        readOnly
                        aria-label="Formatted JSON output text"
                        className="w-full h-full bg-transparent !p-4 text-white font-mono text-sm resize-none focus:outline-none border-none"
                      />
                    )}
                  </>
                ) : (
                  <div className="h-full flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <Code size={32} className="mx-auto! !mb-2 opacity-50" />
                      <p className="text-sm">Formatted JSON will appear here...</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="!mt-8 bg-red-500/10 border border-red-500/30 rounded-xl !p-4"
          >
            <div className="flex items-start gap-3">
              <AlertCircle size={20} className="text-red-400 !mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-red-400 !mb-1">JSON Error</h3>
                <p className="text-red-300">{error.message}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Keyboard Shortcuts Help */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="!mt-12 bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl !p-6"
        >
          <h3 className="text-lg font-semibold text-gray-300 !mb-4">Keyboard Shortcuts</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-3">
              <kbd className="bg-white/10 border border-white/20 rounded !px-2 !py-1 text-xs font-mono">
                Ctrl/Cmd + Enter
              </kbd>
              <span className="text-gray-400">Format JSON</span>
            </div>
            <div className="flex items-center gap-3">
              <kbd className="bg-white/10 border border-white/20 rounded !px-2 !py-1 text-xs font-mono">
                Ctrl/Cmd + K
              </kbd>
              <span className="text-gray-400">Clear all</span>
            </div>
            <div className="flex items-center gap-3">
              <kbd className="bg-white/10 border border-white/20 rounded !px-2 !py-1 text-xs font-mono">
                Ctrl/Cmd + D
              </kbd>
              <span className="text-gray-400">Download JSON</span>
            </div>
            <div className="flex items-center gap-3">
              <kbd className="bg-white/10 border border-white/20 rounded !px-2 !py-1 text-xs font-mono">
                Ctrl/Cmd + C
              </kbd>
              <span className="text-gray-400">Copy to clipboard</span>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default JsonBeautifier;
