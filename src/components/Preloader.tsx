'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import Image from 'next/image';

interface PreloaderProps {
  onComplete: () => void;
}

const Preloader: React.FC<PreloaderProps> = ({ onComplete }) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
      setTimeout(onComplete, 1000); // Wait for exit animation
    }, 1500);

    return () => clearTimeout(timer);
  }, [onComplete]);

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.8, ease: "easeInOut" }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-[#0a0a0a]"
        >
          {/* Background gradient circles */}
          <div className="absolute inset-0 z-0 overflow-hidden">
            {/* Left gradient circle */}
            <div className="absolute top-1/3 -left-32 w-64 h-64 rounded-full bg-gradient-to-r from-purple-500/5 to-violet-500/5 blur-3xl" />
            {/* Right bottom gradient circle */}
            <div className="absolute bottom-1/4 -right-32 w-80 h-80 rounded-full bg-gradient-to-r from-violet-500/5 to-purple-500/5 blur-3xl" />
          </div>

          {/* Logo container */}
          <div className="relative z-10 flex items-center justify-center">
            <div className="relative w-40 h-40">
              {/* Rotating gradient border with shadow */}
              <motion.div
                animate={{ rotate: 360 }}
                transition={{
                  duration: 2,
                  ease: "linear"
                }}
                className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500 via-violet-500 to-purple-500 !p-1 shadow-lg shadow-purple-500/50"
                style={{
                  filter: 'drop-shadow(0 0 20px rgba(139, 92, 246, 0.3)) drop-shadow(0 0 40px rgba(168, 85, 247, 0.2))'
                }}
              />

              {/* Static logo with scale and opacity animation */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{
                  duration: 1,
                  ease: "easeInOut"
                }}
                className="absolute inset-1 rounded-full overflow-hidden bg-white/5 backdrop-blur-sm"
              >
                <Image
                  src="/logo.jpeg"
                  alt="Toolnio Logo"
                  width={96}
                  height={96}
                  className="w-full h-full object-cover"
                  priority
                />
              </motion.div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Preloader;
