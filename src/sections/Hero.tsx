'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Search, Sparkles, Code, Image, Settings, Calculator, FileText, Zap, MapPin, Palette, Shield, Globe } from 'lucide-react';

interface Tool {
  id: string;
  title: string;
  description: string;
  route: string;
  category: string;
  keywords: string[];
}

// Motion particles for background
const MotionParticle = ({ delay, duration, x, y }: { delay: number; duration: number; x: number; y: number }) => (
  <motion.div
    className="absolute w-1 h-1 bg-purple-400/30 rounded-full"
    initial={{ opacity: 0, scale: 0 }}
    animate={{
      opacity: [0, 1, 0],
      scale: [0, 1, 0],
      x: [x, x + Math.random() * 100 - 50],
      y: [y, y + Math.random() * 100 - 50],
    }}
    transition={{
      duration,
      delay,
      ease: "easeInOut"
    }}
  />
);

export const Hero = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [tools, setTools] = useState<Tool[]>([]);
  const [filteredTools, setFilteredTools] = useState<Tool[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [particles, setParticles] = useState<Array<{id: number; delay: number; duration: number; x: number; y: number}>>([]);

  // Generate particles on client side
  useEffect(() => {
    const generateParticles = () => {
      const newParticles = Array.from({ length: 20 }, (_, i) => ({
        id: i,
        delay: Math.random() * 5,
        duration: 8 + Math.random() * 4,
        x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 800),
        y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 600),
      }));
      setParticles(newParticles);
    };

    generateParticles();

    // Regenerate particles on window resize
    const handleResize = () => generateParticles();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  // Load tools data
  useEffect(() => {
    const loadTools = async () => {
      try {
        const response = await fetch('/data/tools.json');
        const data = await response.json();
        setTools(data.tools);
      } catch (error) {
        console.error('Failed to load tools:', error);
      }
    };
    loadTools();
  }, []);

  // Filter tools based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredTools([]);
      setShowSuggestions(false);
      return;
    }

    const filtered = tools.filter(tool =>
      tool.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.keywords.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    setFilteredTools(filtered.slice(0, 6)); // Show max 6 suggestions
    setShowSuggestions(filtered.length > 0);
  }, [searchQuery, tools]);

  const handleToolSelect = (tool: Tool) => {
    setSearchQuery(tool.title);
    setShowSuggestions(false);
    // Navigate to tool route
    if (typeof window !== 'undefined') {
      window.location.href = tool.route;
    }
  };

  // Enhanced floating icons spread across left and right sides
  const floatingIcons = [
    // Left side icons
    { Icon: Code, delay: 0, x: -400, y: -150, size: 32, side: 'left' },
    { Icon: Settings, delay: 0.6, x: -350, y: 80, size: 30, side: 'left' },
    { Icon: FileText, delay: 1.2, x: -450, y: -20, size: 34, side: 'left' },
    { Icon: MapPin, delay: 1.8, x: -380, y: 180, size: 30, side: 'left' },
    { Icon: Shield, delay: 2.4, x: -420, y: 40, size: 26, side: 'left' },
    { Icon: Palette, delay: 3.0, x: -360, y: -100, size: 28, side: 'left' },

    // Right side icons
    { Icon: Image, delay: 0.3, x: 400, y: -120, size: 28, side: 'right' },
    { Icon: Calculator, delay: 0.9, x: 450, y: 60, size: 26, side: 'right' },
    { Icon: Zap, delay: 1.5, x: 380, y: -40, size: 28, side: 'right' },
    { Icon: Globe, delay: 2.7, x: 420, y: 140, size: 28, side: 'right' },
    { Icon: Sparkles, delay: 2.1, x: 360, y: 20, size: 32, side: 'right' },
    { Icon: Search, delay: 3.3, x: 440, y: -80, size: 30, side: 'right' },
  ];

  return (
    <section className="relative min-h-screen flex items-center justify-center !px-4 !py-20 md:!pt-28 overflow-hidden">
      {/* Premium Radial Gradient Background */}
      <div className="absolute inset-0 bg-gradient-radial from-purple-900/30 via-gray-900 to-black" />
      <div className="absolute inset-0 bg-gradient-to-br from-violet-900/20 via-transparent to-purple-900/20" />

      {/* Motion Particles */}
      {particles.map((particle) => (
        <MotionParticle
          key={particle.id}
          delay={particle.delay}
          duration={particle.duration}
          x={particle.x}
          y={particle.y}
        />
      ))}

      {/* Enhanced Gradient Orbs with Premium Glow */}
      <motion.div
        className="absolute top-1/3 left-1/4 w-[500px] h-[500px] bg-gradient-to-r from-purple-600/25 to-violet-600/25 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.4, 0.7, 0.4],
          rotate: [0, 180, 360],
        }}
        transition={{
          duration: 12,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-1/3 right-1/4 w-[400px] h-[400px] bg-gradient-to-r from-violet-600/30 to-purple-600/30 rounded-full blur-3xl"
        animate={{
          scale: [1.2, 1, 1.2],
          opacity: [0.6, 0.4, 0.6],
          rotate: [360, 180, 0],
        }}
        transition={{
          duration: 15,
          ease: "easeInOut"
        }}
      />

      {/* Additional Side Orbs for Better Coverage */}
      <motion.div
        className="absolute top-1/2 left-0 w-[300px] h-[300px] bg-gradient-to-r from-purple-500/20 to-violet-500/20 rounded-full blur-2xl"
        animate={{
          scale: [1, 1.4, 1],
          opacity: [0.3, 0.6, 0.3],
          x: [-50, 50, -50],
        }}
        transition={{
          duration: 18,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute top-1/2 right-0 w-[350px] h-[350px] bg-gradient-to-r from-violet-500/25 to-purple-500/25 rounded-full blur-2xl"
        animate={{
          scale: [1.1, 1, 1.1],
          opacity: [0.4, 0.7, 0.4],
          x: [50, -50, 50],
        }}
        transition={{
          duration: 20,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[300px] h-[300px] bg-gradient-to-r from-purple-500/20 to-violet-500/20 rounded-full blur-2xl"
        animate={{
          scale: [1, 1.4, 1],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 10,
          ease: "easeInOut"
        }}
      />

      {/* Enhanced Floating Icons with Neon Effects */}
      {floatingIcons.map(({ Icon, delay, x, y, size, side }, index) => (
        <motion.div
          key={`floating-icon-`+index}
          className="absolute z-20"
          initial={{ opacity: 0, scale: 0, rotate: -180 }}
          animate={{
            opacity: [0.6, 1, 0.6],
            scale: [1, 1.2, 1],
            rotate: [0, 360],
            x: [x, x + (side === 'left' ? -20 : 20), x - (side === 'left' ? -10 : 10), x],
            y: [y, y - 20, y + 15, y],
          }}
          transition={{
            duration: 12 + index * 0.5,
            delay,
            
            ease: "easeInOut"
          }}
          style={{
            left: `calc(50% + ${x}px)`,
            top: `calc(50% + ${y}px)`,
          }}
        >
          {/* Icon with enhanced neon glow */}
          <div className="relative group">
            {/* Multiple glow layers for intense neon effect */}
            <div className="absolute inset-0 bg-purple-500/40 rounded-full blur-xl scale-150 group-hover:bg-purple-400/60 transition-all duration-300" />
            <div className="absolute inset-0 bg-violet-500/30 rounded-full blur-lg scale-125 group-hover:bg-violet-400/50 transition-all duration-300" />
            <div className="absolute inset-0 bg-purple-400/50 rounded-full blur-md scale-110 group-hover:bg-purple-300/70 transition-all duration-300" />

            {/* Icon container with glassmorphic background */}
            <motion.div
              className="relative bg-white/10 backdrop-blur-md border border-purple-400/30 rounded-2xl p-3 shadow-2xl shadow-purple-500/30"
              whileHover={{
                scale: 1.1,
                boxShadow: "0 0 30px rgba(147, 51, 234, 0.8), 0 0 60px rgba(147, 51, 234, 0.4)"
              }}
              animate={{
                boxShadow: [
                  "0 0 20px rgba(147, 51, 234, 0.3)",
                  "0 0 40px rgba(147, 51, 234, 0.6)",
                  "0 0 20px rgba(147, 51, 234, 0.3)"
                ]
              }}
              transition={{
                boxShadow: { duration: 3, ease: "easeInOut" },
                scale: { duration: 0.3 }
              }}
            >
              <Icon
                size={size}
                className="text-white drop-shadow-[0_0_10px_rgba(147,51,234,0.8)]"
              />
            </motion.div>
          </div>
        </motion.div>
      ))}

      {/* Additional Decorative Elements for Side Areas */}
      {/* Left side decorative circles */}
      <motion.div
        className="absolute top-1/4 left-16 w-4 h-4 bg-purple-400/60 rounded-full"
        animate={{
          opacity: [0.4, 1, 0.4],
          scale: [1, 1.5, 1],
        }}
        transition={{
          duration: 4,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute top-3/4 left-24 w-3 h-3 bg-violet-400/50 rounded-full"
        animate={{
          opacity: [0.3, 0.8, 0.3],
          scale: [1, 1.3, 1],
        }}
        transition={{
          duration: 5,
          delay: 1,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute top-1/2 left-8 w-2 h-2 bg-purple-300/70 rounded-full"
        animate={{
          opacity: [0.5, 1, 0.5],
          scale: [1, 1.8, 1],
        }}
        transition={{
          duration: 3,
          delay: 2,
          ease: "easeInOut"
        }}
      />

      {/* Right side decorative circles */}
      <motion.div
        className="absolute top-1/3 right-16 w-4 h-4 bg-violet-400/60 rounded-full"
        animate={{
          opacity: [0.4, 1, 0.4],
          scale: [1, 1.5, 1],
        }}
        transition={{
          duration: 4.5,
          delay: 0.5,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute top-2/3 right-24 w-3 h-3 bg-purple-400/50 rounded-full"
        animate={{
          opacity: [0.3, 0.8, 0.3],
          scale: [1, 1.3, 1],
        }}
        transition={{
          duration: 5.5,
          delay: 1.5,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute top-1/6 right-8 w-2 h-2 bg-violet-300/70 rounded-full"
        animate={{
          opacity: [0.5, 1, 0.5],
          scale: [1, 1.8, 1],
        }}
        transition={{
          duration: 3.5,
          delay: 2.5,
          ease: "easeInOut"
        }}
      />

      {/* Elite Main Content */}
      <div className="relative z-10 text-center max-w-7xl mx-auto!">
        {/* Premium Animated Badge */}
        <motion.div
          initial={{ opacity: 0, y: -30, scale: 0.8 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="!mb-12"
        >
          <motion.div
            className="inline-flex items-center gap-3 !px-6 !py-3 rounded-full bg-gradient-to-r from-purple-600/20 to-violet-600/20 border border-purple-400/30 backdrop-blur-xl shadow-2xl shadow-purple-500/20"
            whileHover={{ scale: 1.05, boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.4)" }}
            transition={{ duration: 0.3 }}
          >
            <motion.span
              animate={{
                rotate: [0, 15, -15, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ duration: 3 }}
              className="text-lg"
            >
              🚀
            </motion.span>
            <span className="text-sm font-semibold text-purple-100 tracking-wide">
              July Update – Save Now
            </span>
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 4,  ease: "linear" }}
            >
              <Sparkles className="w-5 h-5 text-purple-300" />
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Elite Impactful Headline */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
          className="!mb-8"
        >
          <h1 className="text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-black leading-[0.9] tracking-tight">
            <motion.span
              className="block text-white"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              Your Ultimate
            </motion.span>
            <motion.span
              className="block bg-gradient-to-r from-purple-400 via-violet-300 to-purple-400 bg-clip-text text-transparent relative"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.7 }}
              style={{
                backgroundSize: "200% 100%",
                animation: "gradient-shift 3s ease-in-out infinite"
              }}
            >
              AI-Powered
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-purple-400/20 via-violet-300/20 to-purple-400/20 blur-xl"
                animate={{
                  opacity: [0.5, 1, 0.5],
                  scale: [1, 1.05, 1],
                }}
                transition={{
                  duration: 2,
                  
                  ease: "easeInOut"
                }}
              />
            </motion.span>
            <motion.span
              className="block text-white"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.9 }}
            >
              Productivity Suite
            </motion.span>
          </h1>
        </motion.div>

        {/* Elite Professional Subtext */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1.1 }}
          className="!mb-16"
        >
          <p className="text-xl md:text-xl text-gray-300 max-w-4xl mx-auto! leading-relaxed font-light">
            Experience the future of productivity with our{' '}
            <span className="text-purple-300 font-medium">intelligent collection</span> of
            premium tools. From advanced{' '}
            <span className="text-violet-300 font-medium">image compression</span> and{' '}
            <span className="text-purple-300 font-medium">JSON conversion</span> to
            sophisticated{' '}
            <span className="text-violet-300 font-medium">coordinate calculations</span> and{' '}
            <span className="text-purple-300 font-medium">professional invoice generation</span>
            {' '}— engineered to save time and boost efficiency.
          </p>
        </motion.div>

        {/* Elite Glassmorphic Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 40, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 1, delay: 1.3 }}
          className="relative max-w-3xl mx-auto! !mb-12"
        >
          <motion.div
            className="relative group"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-violet-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300" />
            <div className="relative bg-white/[0.08] backdrop-blur-2xl border border-white/20 rounded-3xl shadow-2xl shadow-purple-500/10">
              <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-purple-300 w-6 h-6 group-hover:text-purple-200 transition-colors duration-300" />
              <input
                type="text"
                placeholder="Search tools like image compressor, invoice maker..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => setShowSuggestions(filteredTools.length > 0)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                className="w-full !pl-16 !pr-6 !py-6 bg-transparent text-white text-lg placeholder-gray-400 focus:outline-none focus:placeholder-gray-500 transition-all duration-300 relative z-50"
              />
              <div className="absolute inset-0 rounded-3xl ring-1 ring-white/10 group-hover:ring-purple-400/30 focus-within:ring-purple-400/50 transition-all duration-300" />
            </div>
          </motion.div>

          {/* Enhanced Search Suggestions */}
          {showSuggestions && filteredTools.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.3 }}
              className="absolute top-full left-0 right-0 !mt-4 bg-purple-900/20 backdrop-blur-2xl border border-white/20 rounded-2xl overflow-hidden z-50 shadow-2xl shadow-purple-500/20"
            >
              {filteredTools.map((tool, index) => (
                <motion.button
                  key={tool.id}
                  onClick={() => handleToolSelect(tool)}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="w-full !px-6 !py-4 text-left hover:bg-purple-500/10 transition-all duration-300 border-b border-white/10 last:border-b-0 group"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-white font-semibold group-hover:text-purple-200 transition-colors duration-300">
                        {tool.title}
                      </div>
                      <div className="text-gray-400 text-sm !mt-1 group-hover:text-gray-300 transition-colors duration-300">
                        {tool.description}
                      </div>
                    </div>
                    <div className="text-purple-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <Search className="w-4 h-4" />
                    </div>
                  </div>
                </motion.button>
              ))}
            </motion.div>
          )}
        </motion.div>

        {/* Elite Glowing CTA Button */}
        <motion.div
          initial={{ opacity: 0, y: 30, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 1, delay: 1.5 }}
          className="!mb-20"
        >
          <motion.button
            className="group relative !px-12 !py-5 bg-gradient-to-r from-purple-600 via-violet-600 to-purple-600 text-white font-bold text-lg rounded-3xl overflow-hidden"
            whileHover={{
              scale: 1.05,
              boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.6)"
            }}
            whileTap={{ scale: 0.98 }}
            transition={{ duration: 0.3 }}
          >
            <span className="relative z-10 flex items-center gap-3">
              Explore Tools
              <motion.div
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 1.5 }}
              >
                →
              </motion.div>
            </span>

            {/* Multiple glow layers for premium effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-violet-600 to-purple-600 opacity-75 group-hover:opacity-100 transition-opacity duration-300" />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-purple-400 via-violet-400 to-purple-400 blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-300"
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.5, 0.8, 0.5],
              }}
              transition={{
                duration: 3,
                
                ease: "easeInOut"
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-transparent to-white/20 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
          </motion.button>
        </motion.div>

        {/* Premium Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1.7 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-5xl mx-auto!"
        >
          {[
            { number: "15+", label: "AI-Powered Tools", delay: 0 },
            { number: "100%", label: "Free & Premium", delay: 0.1 },
            { number: "24/7", label: "Always Available", delay: 0.2 },
          ].map((stat, index) => (
            <motion.div
              key={`stat-`+index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.9 + stat.delay }}
              className="text-center group"
            >
              <motion.div
                className="text-4xl md:text-5xl font-black text-transparent bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text !mb-3"
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.3 }}
              >
                {stat.number}
              </motion.div>
              <div className="text-gray-300 font-medium text-lg group-hover:text-purple-200 transition-colors duration-300">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
