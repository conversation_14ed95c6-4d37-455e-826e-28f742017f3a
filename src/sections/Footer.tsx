'use client';

import { motion } from 'framer-motion';
import {
  Twitter,
  Github,
  Linkedin,
  Mail,
  MapPin,
  Phone,
  ArrowRight,
  Heart,
  Zap,
  Shield,
  Globe,
  ExternalLink
} from 'lucide-react';

interface FloatingParticleProps {
  delay: number;
  duration: number;
  x: number;
  y: number;
}

const FloatingParticle: React.FC<FloatingParticleProps> = ({ delay, duration, x, y }) => (
  <motion.div
    className="absolute w-1 h-1 bg-gradient-to-r from-purple-400 to-violet-400 rounded-full opacity-20"
    style={{ left: x, top: y }}
    animate={{
      y: [0, -20, 0],
      x: [0, 10, -10, 0],
      opacity: [0.2, 0.6, 0.2],
      scale: [1, 1.2, 1]
    }}
    transition={{
      duration,
      delay,
      ease: "easeInOut"
    }}
  />
);

interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
  external?: boolean;
}

const FooterLink: React.FC<FooterLinkProps> = ({ href, children, external = false }) => (
  <motion.a
    href={href}
    target={external ? "_blank" : undefined}
    rel={external ? "noopener noreferrer" : undefined}
    className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center gap-2 group"
    whileHover={{ x: 5 }}
    transition={{ duration: 0.3 }}
  >
    {children}
    {external && <ExternalLink size={14} className="opacity-0 group-hover:opacity-100 transition-opacity duration-300" />}
  </motion.a>
);

interface SocialLinkProps {
  href: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  gradient: string;
}

const SocialLink: React.FC<SocialLinkProps> = ({ href, icon: Icon, label, gradient }) => (
  <motion.a
    href={href}
    target="_blank"
    rel="noopener noreferrer"
    className="group relative"
    whileHover={{ scale: 1.1, y: -2 }}
    whileTap={{ scale: 0.95 }}
    transition={{ duration: 0.3 }}
    aria-label={label}
  >
    <motion.div
      className={`absolute inset-0 bg-gradient-to-r ${gradient} rounded-xl blur-lg opacity-0 group-hover:opacity-60 transition-all duration-500`}
      whileHover={{ scale: 1.2 }}
    />
    <div className="relative w-12 h-12 bg-white/[0.05] backdrop-blur-xl border border-white/10 rounded-xl flex items-center justify-center group-hover:bg-white/[0.1] transition-all duration-300">
      <Icon size={20} className="text-gray-400 group-hover:text-white transition-colors duration-300" />
    </div>
  </motion.a>
);

interface NewsletterFormProps {
  onSubmit: (email: string) => void;
}

const NewsletterForm: React.FC<NewsletterFormProps> = ({ onSubmit }) => {
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const email = formData.get('email') as string;
    onSubmit(email);
  };

  return (
    <motion.form
      onSubmit={handleSubmit}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8, delay: 0.4 }}
      className="relative group"
    >
      <div className="relative">
        <input
          type="email"
          name="email"
          placeholder="Enter your email"
          required
          className="w-full !px-6 !py-4 bg-white/[0.05] backdrop-blur-xl border border-white/10 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-400/50 focus:bg-white/[0.08] transition-all duration-300"
        />
        <motion.button
          type="submit"
          className="absolute right-2 top-1/2 -translate-y-1/2 !px-6 !py-2 bg-gradient-to-r from-purple-600 to-violet-600 text-white font-semibold rounded-xl hover:from-purple-500 hover:to-violet-500 transition-all duration-300 flex items-center gap-2"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Subscribe
          <ArrowRight size={16} />
        </motion.button>
      </div>
    </motion.form>
  );
};

export const Footer = () => {
  // Generate floating particles
  const particles = Array.from({ length: 20 }, (_, i) => ({
    id: i,
    delay: Math.random() * 4,
    duration: 8 + Math.random() * 4,
    x: Math.random() * 1200,
    y: Math.random() * 400,
  }));

  const handleNewsletterSubmit = (email: string) => {
    console.log('Newsletter subscription:', email);
    // Handle newsletter subscription logic here
  };

  const footerLinks = {
    product: [
      { label: "All Tools", href: "/tools" },
      { label: "AI Generator", href: "/tools/ai-generator" },
      { label: "Resume Builder", href: "/tools/resume-generator" },
      { label: "Invoice Creator", href: "/tools/invoice-generator" },
      { label: "Paraphrase Tool", href: "/tools/paraphrase" }
    ],
    company: [
      { label: "About Us", href: "/about" },
      { label: "Use Cases", href: "/use-cases" },
      { label: "Pricing", href: "/pricing" },
      { label: "Blog", href: "/blog" },
      { label: "Careers", href: "/careers" }
    ],
    support: [
      { label: "Help Center", href: "/help" },
      { label: "Contact Us", href: "/contact" },
      { label: "API Docs", href: "/docs", external: true },
      { label: "Status", href: "/status", external: true },
      { label: "Community", href: "/community" }
    ],
    legal: [
      { label: "Privacy Policy", href: "/privacy" },
      { label: "Terms of Service", href: "/terms" },
      { label: "Cookie Policy", href: "/cookies" },
      { label: "GDPR", href: "/gdpr" }
    ]
  };

  const socialLinks = [
    {
      href: "https://twitter.com/toolnio",
      icon: Twitter,
      label: "Twitter",
      gradient: "from-blue-400 to-blue-600"
    },
    {
      href: "https://github.com/toolnio",
      icon: Github,
      label: "GitHub",
      gradient: "from-gray-400 to-gray-600"
    },
    {
      href: "https://linkedin.com/company/toolnio",
      icon: Linkedin,
      label: "LinkedIn",
      gradient: "from-blue-500 to-blue-700"
    },
    {
      href: "mailto:<EMAIL>",
      icon: Mail,
      label: "Email",
      gradient: "from-purple-500 to-violet-600"
    }
  ];

  return (
    <footer className="relative !pt-32 !pb-12 !px-4 overflow-hidden border-t border-white/10">
      {/* Premium Radial Gradient Background */}
      <div className="absolute inset-0 bg-gradient-radial from-purple-900/20 via-gray-900 to-black" />
      <div className="absolute inset-0 bg-gradient-to-t from-black via-gray-900/50 to-transparent" />

      {/* Floating Particles */}
      {particles.map((particle) => (
        <FloatingParticle
          key={particle.id}
          delay={particle.delay}
          duration={particle.duration}
          x={particle.x}
          y={particle.y}
        />
      ))}

      <div className="relative z-10 max-w-7xl mx-auto!">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 !mb-16">
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="lg:col-span-4"
          >
            {/* Logo */}
            <motion.div
              className="flex items-center gap-4 !mb-6"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-purple-500 to-violet-500 flex items-center justify-center shadow-lg shadow-purple-500/30">
                <span className="text-white font-bold text-xl">T</span>
              </div>
              <h3 className="text-3xl font-bold text-transparent bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text">
                Toolnio
              </h3>
            </motion.div>

            <p className="text-gray-300 leading-relaxed !mb-8 text-lg">
              Empowering creators and professionals with AI-powered tools that streamline workflows
              and unlock creative potential. Join thousands who've transformed their productivity.
            </p>

            {/* Newsletter Signup */}
            <div className="!mb-8">
              <h4 className="text-white font-semibold !mb-4 flex items-center gap-2">
                <Zap size={20} className="text-purple-400" />
                Stay Updated
              </h4>
              <NewsletterForm onSubmit={handleNewsletterSubmit} />
            </div>

            {/* Social Links */}
            <div className="flex gap-4">
              {socialLinks.map((social) => (
                <SocialLink
                  key={social.label}
                  href={social.href}
                  icon={social.icon}
                  label={social.label}
                  gradient={social.gradient}
                />
              ))}
            </div>
          </motion.div>

          {/* Links Sections */}
          <div className="lg:col-span-8 grid grid-cols-2 md:grid-cols-4 gap-8">
            {/* Product Links */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.1 }}
            >
              <h4 className="text-white font-semibold !mb-6">Product</h4>
              <ul className="space-y-4">
                {footerLinks.product.map((link) => (
                  <li key={link.label}>
                    <FooterLink href={link.href}>{link.label}</FooterLink>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Company Links */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <h4 className="text-white font-semibold !mb-6">Company</h4>
              <ul className="space-y-4">
                {footerLinks.company.map((link) => (
                  <li key={link.label}>
                    <FooterLink href={link.href}>{link.label}</FooterLink>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Support Links */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <h4 className="text-white font-semibold !mb-6">Support</h4>
              <ul className="space-y-4">
                {footerLinks.support.map((link) => (
                  <li key={link.label}>
                    <FooterLink href={link.href} external={link.external}>
                      {link.label}
                    </FooterLink>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Legal Links */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <h4 className="text-white font-semibold !mb-6">Legal</h4>
              <ul className="space-y-4">
                {footerLinks.legal.map((link) => (
                  <li key={link.label}>
                    <FooterLink href={link.href}>{link.label}</FooterLink>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="!pt-8 border-t border-white/10"
        >
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            {/* Copyright */}
            <div className="flex items-center gap-2 text-gray-400">
              <span>© {new Date().getFullYear()} Toolnio. Built with</span>
              <Heart size={16} className="text-red-400 fill-current" />
              <span>for creators worldwide.</span>
            </div>

            {/* Trust Badges */}
            <div className="flex items-center gap-6 text-gray-400">
              <div className="flex items-center gap-2">
                <Shield size={16} className="text-green-400" />
                <span className="text-sm">SSL Secured</span>
              </div>
              <div className="flex items-center gap-2">
                <Globe size={16} className="text-blue-400" />
                <span className="text-sm">Global CDN</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap size={16} className="text-yellow-400" />
                <span className="text-sm">99.9% Uptime</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
