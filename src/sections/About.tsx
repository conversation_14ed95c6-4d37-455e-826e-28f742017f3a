'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Zap, Shield, Users, Target, Rocket, Code, Heart, Star, Award } from 'lucide-react';

interface FloatingParticleProps {
  delay: number;
  duration: number;
  x: number;
  y: number;
}

const FloatingParticle: React.FC<FloatingParticleProps> = ({ delay, duration, x, y }) => (
  <motion.div
    className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-violet-400 rounded-full opacity-30"
    style={{ left: x, top: y }}
    animate={{
      y: [0, -30, 0],
      x: [0, 15, -15, 0],
      opacity: [0.3, 0.8, 0.3],
      scale: [1, 1.2, 1]
    }}
    transition={{
      duration,
      delay,
      ease: "easeInOut"
    }}
  />
);

interface FeatureCardProps {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  title: string;
  description: string;
  gradient: string;
  delay: number;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon: Icon, title, description, gradient, delay }) => (
  <motion.div
    initial={{ opacity: 0, y: 60, scale: 0.9 }}
    whileInView={{ opacity: 1, y: 0, scale: 1 }}
    viewport={{ once: true, margin: "-100px" }}
    transition={{ duration: 0.8, delay, ease: [0.25, 0.46, 0.45, 0.94] }}
    whileHover={{
      scale: 1.05,
      rotateY: 5,
      transition: { duration: 0.3 }
    }}
    className="group relative"
  >
    {/* Enhanced Card Glow Effect */}
    <motion.div
      className={`absolute inset-0 bg-gradient-to-r ${gradient} rounded-3xl blur-2xl opacity-0 group-hover:opacity-80 transition-all duration-500`}
      whileHover={{ scale: 1.15 }}
    />
    <motion.div
      className={`absolute inset-0 bg-gradient-to-r ${gradient} rounded-3xl blur-xl opacity-20 group-hover:opacity-60 transition-all duration-500`}
      whileHover={{ scale: 1.1 }}
    />
    
    {/* Main Card */}
    <div className="relative bg-white/[0.08] backdrop-blur-xl border border-white/20 rounded-3xl !p-8 shadow-2xl shadow-purple-500/10 group-hover:shadow-purple-500/20 transition-all duration-500">
      {/* Icon Container */}
      <motion.div
        className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${gradient} !p-4 !mb-6 shadow-lg`}
        whileHover={{ 
          scale: 1.1,
          rotate: 5,
          boxShadow: "0 20px 40px -12px rgba(147, 51, 234, 0.4)"
        }}
        transition={{ duration: 0.3 }}
      >
        <Icon size={32} className="text-white" />
      </motion.div>
      
      {/* Content */}
      <h3 className="text-xl font-bold text-white !mb-4 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-violet-400 group-hover:bg-clip-text transition-all duration-300">
        {title}
      </h3>
      <p className="text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
        {description}
      </p>
    </div>
  </motion.div>
);

interface StatCardProps {
  number: string;
  label: string;
  delay: number;
}

const StatCard: React.FC<StatCardProps> = ({ number, label, delay }) => (
  <motion.div
    initial={{ opacity: 0, y: 40, scale: 0.9 }}
    whileInView={{ opacity: 1, y: 0, scale: 1 }}
    viewport={{ once: true }}
    transition={{ duration: 0.8, delay }}
    whileHover={{ scale: 1.05, y: -5 }}
    className="group text-center"
  >
    <motion.div
      className="relative bg-white/[0.05] backdrop-blur-xl border border-white/10 rounded-2xl !p-8 shadow-xl shadow-purple-500/5 group-hover:shadow-purple-500/20 transition-all duration-500"
      whileHover={{
        boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.3)"
      }}
    >
      <motion.div
        className="text-4xl md:text-5xl font-black text-transparent bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text !mb-3"
        animate={{
          backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
        }}
        transition={{ duration: 3 }}
        style={{ backgroundSize: "200% 200%" }}
      >
        {number}
      </motion.div>
      <p className="text-gray-300 font-medium group-hover:text-white transition-colors duration-300">
        {label}
      </p>
    </motion.div>
  </motion.div>
);

export const About = () => {
  // Generate floating particles
  const particles = Array.from({ length: 20 }, (_, i) => ({
    id: i,
    delay: Math.random() * 4,
    duration: 6 + Math.random() * 3,
    x: Math.random() * 1200,
    y: Math.random() * 800,
  }));

  const features = [
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Built with cutting-edge technology for instant results and seamless user experience.",
      gradient: "from-yellow-500 to-orange-500"
    },
    {
      icon: Shield,
      title: "Secure & Private",
      description: "Your data is protected with enterprise-grade security and privacy-first design.",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      icon: Users,
      title: "User-Centric",
      description: "Designed with real users in mind, focusing on simplicity and powerful functionality.",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: Target,
      title: "Precision Tools",
      description: "Each tool is crafted with precision to solve specific problems efficiently.",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      icon: Rocket,
      title: "Innovation First",
      description: "Constantly evolving with the latest AI and technology advancements.",
      gradient: "from-indigo-500 to-purple-500"
    },
    {
      icon: Heart,
      title: "Made with Love",
      description: "Every feature is built with passion and attention to detail for the best experience.",
      gradient: "from-red-500 to-pink-500"
    }
  ];

  const stats = [
    { number: "50K+", label: "Happy Users", delay: 0 },
    { number: "15+", label: "AI Tools", delay: 0.1 },
    { number: "99.9%", label: "Uptime", delay: 0.2 },
    { number: "24/7", label: "Support", delay: 0.3 }
  ];

  return (
    <section id="about" className="relative !py-32 !px-4 overflow-hidden">
      {/* Premium Radial Gradient Background */}
      <div className="absolute inset-0 bg-gradient-radial from-purple-900/30 via-gray-900 to-black" />
      <div className="absolute inset-0 bg-gradient-to-br from-violet-900/20 via-transparent to-indigo-900/20" />
      
      {/* Floating Particles */}
      {particles.map((particle) => (
        <FloatingParticle
          key={particle.id}
          delay={particle.delay}
          duration={particle.duration}
          x={particle.x}
          y={particle.y}
        />
      ))}

      <div className="relative z-10 max-w-7xl mx-auto!">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="text-center !mb-20"
        >
          {/* Premium Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="!mb-8"
          >
            <motion.div
              className="inline-flex items-center gap-3 !px-6 !py-3 rounded-full bg-gradient-to-r from-purple-600/20 to-violet-600/20 border border-purple-400/30 backdrop-blur-xl shadow-2xl shadow-purple-500/20"
              whileHover={{ scale: 1.05, boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.4)" }}
            >
              <Sparkles size={20} className="text-purple-400" />
              <span className="text-purple-300 font-semibold">About Toolnio</span>
            </motion.div>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-4xl md:text-6xl font-black !mb-8"
          >
            <span className="text-transparent bg-gradient-to-r from-purple-400 via-violet-400 to-purple-400 bg-clip-text">
              Empowering Creators
            </span>
            <br />
            <span className="text-white">with AI Innovation</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-xl text-gray-300 max-w-4xl mx-auto! leading-relaxed"
          >
            Toolnio is more than just a collection of tools – it's a comprehensive platform designed to 
            streamline your workflow, boost productivity, and unlock creative potential through the power of AI.
          </motion.p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 0.8 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 !mb-24"
        >
          {features.map((feature, index) => (
            <FeatureCard
              key={feature.title}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              gradient={feature.gradient}
              delay={index * 0.1}
            />
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="!mb-20"
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-center !mb-16"
          >
            <h3 className="text-3xl md:text-4xl font-bold text-white !mb-4">
              Trusted by <span className="text-transparent bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text">Thousands</span>
            </h3>
            <p className="text-gray-300 text-lg">
              Join our growing community of creators and professionals
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <StatCard
                key={stat.label}
                number={stat.number}
                label={stat.label}
                delay={stat.delay}
              />
            ))}
          </div>
        </motion.div>

        {/* Mission Statement */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1 }}
          className="relative"
        >
          <motion.div
            className="relative bg-gradient-to-r from-purple-600/10 to-violet-600/10 backdrop-blur-xl border border-white/10 rounded-3xl !p-12 md:!p-16 shadow-2xl shadow-purple-500/10"
            whileHover={{
              boxShadow: "0 25px 50px -12px rgba(147, 51, 234, 0.2)"
            }}
          >
            <div className="text-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="!mb-8"
              >
                <div className="w-20 h-20 rounded-full bg-gradient-to-r from-purple-500 to-violet-500 flex items-center justify-center mx-auto! !mb-6 shadow-lg shadow-purple-500/30">
                  <Award size={40} className="text-white" />
                </div>
              </motion.div>

              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-3xl md:text-4xl font-bold text-white !mb-6"
              >
                Our <span className="text-transparent bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text">Mission</span>
              </motion.h3>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="text-xl text-gray-300 max-w-4xl mx-auto! leading-relaxed"
              >
                To democratize access to powerful AI tools, making advanced technology simple,
                accessible, and affordable for everyone. We believe that great tools should empower
                creativity, not complicate it.
              </motion.p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
