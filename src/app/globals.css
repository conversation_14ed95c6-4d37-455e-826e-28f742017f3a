@import "tailwindcss";

:root {
  /* Dark theme colors */
  --background: #0a0a0a;
  --foreground: #ededed;

  /* Purple/Violet gradient branding */
  --primary-purple: #8b5cf6;
  --primary-violet: #a855f7;
  --gradient-primary: linear-gradient(135deg, var(--primary-purple), var(--primary-violet));
  --gradient-secondary: linear-gradient(135deg, #6366f1, #8b5cf6);

  /* Additional dark theme colors */
  --surface: #1a1a1a;
  --surface-light: #2a2a2a;
  --border: #333333;
  --text-muted: #a1a1aa;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Smooth scrolling will be handled by Lenis */
html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-secondary);
}

/* Premium gradient animations for hero */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Radial gradient utility */
.bg-gradient-radial {
  background: radial-gradient(circle at center, var(--tw-gradient-stops));
}

/* Enhanced glassmorphism effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced Premium glow effects */
.glow-purple {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.4),
              0 0 40px rgba(147, 51, 234, 0.3),
              0 0 80px rgba(147, 51, 234, 0.2),
              0 0 120px rgba(147, 51, 234, 0.1);
}

.glow-violet {
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.4),
              0 0 40px rgba(168, 85, 247, 0.3),
              0 0 80px rgba(168, 85, 247, 0.2),
              0 0 120px rgba(168, 85, 247, 0.1);
}

/* Intense neon glow for floating icons */
.neon-glow-intense {
  filter: drop-shadow(0 0 10px rgba(147, 51, 234, 0.8))
          drop-shadow(0 0 20px rgba(147, 51, 234, 0.6))
          drop-shadow(0 0 30px rgba(147, 51, 234, 0.4));
}

/* Pulsing neon animation */
@keyframes neon-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(147, 51, 234, 0.8))
            drop-shadow(0 0 15px rgba(147, 51, 234, 0.6))
            drop-shadow(0 0 25px rgba(147, 51, 234, 0.4));
  }
  50% {
    filter: drop-shadow(0 0 10px rgba(147, 51, 234, 1))
            drop-shadow(0 0 25px rgba(147, 51, 234, 0.8))
            drop-shadow(0 0 40px rgba(147, 51, 234, 0.6));
  }
}

.neon-pulse {
  animation: neon-pulse 2s ease-in-out infinite;
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
