import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono, Inter } from "next/font/google";
import "./globals.css";
import ClientLayout from "../components/ClientLayout";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Toolnio - Your Ultimate Utility Tools Suite",
  description: "Modern utility tools suite featuring image compression, array converters, coordinate calculators, and more. Built with cutting-edge technology for seamless user experience.",
  keywords: "utility tools, image compressor, array converter, coordinate calculator, invoice maker, web tools",
  authors: [{ name: "Toolnio Team" }],
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${inter.variable} antialiased`}
      >
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
