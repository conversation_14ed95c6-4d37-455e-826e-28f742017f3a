import { Metadata } from 'next';
import ImageCompressor from '@/components/tools/ImageCompressor';

export const metadata: Metadata = {
  title: 'Image Compressor - Compress Images Without Quality Loss | Toolnio',
  description: 'Compress JPEG, PNG, and WebP images without losing quality. Adjust compression levels, preview results, and download optimized images. Free online image compression tool.',
  keywords: 'image compressor, compress images, optimize images, jpeg compression, png compression, webp compression, reduce file size',
};

export default function ImageCompressorPage() {
  return (
    <div className="min-h-screen bg-[#0a0a0a] text-white relative">
      <ImageCompressor />
    </div>
  );
}
