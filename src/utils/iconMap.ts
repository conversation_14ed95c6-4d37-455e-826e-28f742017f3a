import {
  Baby,
  FileText,
  RotateCcw,
  Receipt,
  Image,
  Code,
  MapPin,
  QrCode,
  Shield,
  Palette,
  Type,
  Link,
  Binary,
  FileEdit,
  Hash,
  Calculator,
  Settings,
  Zap,
  Globe,
  Search,
  Sparkles,
  Braces
} from 'lucide-react';

// Comprehensive icon mapping for dynamic icon loading
export const iconMap: Record<string, React.ComponentType<{ size?: number; className?: string }>> = {
  Baby,
  FileText,
  RotateCcw,
  Receipt,
  Image,
  Code,
  MapPin,
  QrCode,
  Shield,
  Palette,
  Type,
  Link,
  Binary,
  FileEdit,
  Hash,
  Calculator,
  Settings,
  Zap,
  Globe,
  Search,
  Sparkles,
  Braces
};

// Helper function to get icon component by name
export const getIconComponent = (iconName: string) => {
  return iconMap[iconName] || FileText; // Fallback to FileText if icon not found
};

// Helper function to get all available icon names
export const getAvailableIcons = () => {
  return Object.keys(iconMap);
};
